<template>
  <div
    class="agent-chat-message"
    :class="{ 'is-user': messageData.role === 'user' }"
    :data-message-key="messageData.key"
  >
    <div class="message-container">
      <div
        class="message-content"
        :class="{
          'loading-content': !messageData.isFinish && messageData.role === 'assistant',
        }"
      >
        <!-- 操作栏 (仅在 role=assistant 时显示) -->
        <div
          v-if="messageData.isFinish && messageData.role === 'assistant' && messageData.content"
          class="action-bar"
        >
          <div class="timestamp">
            {{ formatTime(messageData.key) }}
          </div>
          <div class="action-buttons">
            <span class="action-item" @click="handleCopy">
              📋 {{ isCopied ? '已复制' : '复制' }}
            </span>
            <span class="action-item" @click="handleForward">
              📤 转发
            </span>
            <span class="action-item" @click="handleRead">
              🔊 朗读
            </span>
            <span class="action-item" @click="handleReview">
              🔍 复盘
            </span>
          </div>
        </div>
        <template
          v-if="
            messageData.isFinish ||
            messageData.content ||
            messageData.reasoningData.content ||
            messageData.preResponseContent
          "
        >
          <template v-if="messageData.role === 'assistant'">
            <AgentThinking
              v-if="messageData.reasoningData.content"
              :reasoning-data="messageData.reasoningData"
            />
            <!-- 预响应内容显示 -->
            <div
              v-if="messageData.preResponseContent && !messageData.content"
              class="pre-response-content"
            >
              <AgentMessageRender :text="messageData.preResponseContent" />
            </div>
            <!-- 正式回答内容显示 - 使用打字机效果 -->
            <AgentMessageRender
              v-if="displayContent || (messageData.isFinish && messageData.content)"
              :text="displayContent || messageData.content"
            />
          </template>
          <span v-else class="user-message-content">{{ messageData.content }}</span>
        </template>
        <div v-else-if="!messageData.isFinish && messageData.role === 'assistant'" class="loading">
          <span class="loading-dots">
            <span class="dot dot1">.</span>
            <span class="dot dot2">.</span>
            <span class="dot dot3">.</span>
          </span>
        </div>
      </div>
    </div>
  </div>

  <!-- 复盘弹窗 -->
  <ReviewDialog
    v-if="showReviewDialog"
    @close="closeReviewDialogs"
    @submit="handleReviewSubmit"
  />

  <!-- 复盘确认弹窗 -->
  <ReviewConfirmDialog
    v-if="showReviewConfirmDialog"
    :review-suggestion="reviewSuggestion"
    @close="closeReviewDialogs"
    @confirm="handleReviewConfirm"
  />
</template>

<script setup lang="ts">
import { defineProps, ref, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { AgentTypewriter } from '../typewriter';
import AgentMessageRender from './AgentMessageRender.vue';
import AgentThinking from './AgentThinking.vue';
import ReviewDialog from './ReviewDialog.vue';
import ReviewConfirmDialog from './ReviewConfirmDialog.vue';
import { useAudioQueue } from '@/pages/Chat/useAudioPlayer';

interface IProps {
  messageData: IChatStreamContent;
  originalQuestion?: string; // 添加原始问题属性
}

const props = defineProps<IProps>();

// Emits定义
const emit = defineEmits<{
  sendMessage: [content: string];
}>();

// 显示用的内容状态（用于打字机效果）
const displayContent = ref('');

// 使用美团TTS音频播放器
const { play, stop, isCurrentAudioPlaying, audioStatus } = useAudioQueue();

// 生成唯一的TTS ID
const ttsId = `agent-chat`;

// 朗读状态
const isReading = ref(false);

// 复制状态
const isCopied = ref(false);

// 复盘弹窗状态
const showReviewDialog = ref(false);
const showReviewConfirmDialog = ref(false);
const reviewSuggestion = ref('');

// 打字机实例
const typewriter = new AgentTypewriter({
  speed: 25, // 更快的速度，25ms/字符
  onUpdate: (content: string) => {
    console.log('🖨️ [AgentChatItem] typewriter更新消息内容:', {
      displayLength: content.length,
      preview: content.substring(0, 50) + (content.length > 50 ? '...' : ''),
    });
    displayContent.value = content;
  },
  onComplete: () => {
    console.log('✅ [AgentChatItem] typewriter完成回调触发');
    // 确保显示完整内容
    if (props.messageData.content && displayContent.value !== props.messageData.content) {
      displayContent.value = props.messageData.content;
    }
  },
});

// 监听messageData.content的变化，实现打字机效果
watch(
  () => props.messageData.content,
  (newContent, oldContent) => {
    if (props.messageData.role === 'assistant' && newContent && newContent !== oldContent) {
      // 只有未完成的消息才使用打字机效果
      if (!props.messageData.isFinish) {
        console.log('🔄 [AgentChatItem] 检测到新的assistant内容，启动打字机效果');

        // 设置内容并启动打字机
        typewriter.setContent(newContent);
        typewriter.start();
      } else {
        // 已完成的消息直接显示完整内容（历史对话）
        console.log('🔄 [AgentChatItem] 检测到已完成的assistant内容，直接显示');
        displayContent.value = newContent;
      }
    }
  },
  { immediate: true }
);

// 监听messageData.isFinish的变化
watch(
  () => props.messageData.isFinish,
  async (isFinish) => {
    if (isFinish && props.messageData.role === 'assistant' && props.messageData.content) {
      console.log('🔄 [AgentChatItem] 消息完成，确保显示完整内容');
      // 如果消息已完成但还没有显示完整内容，直接显示完整内容
      if (displayContent.value !== props.messageData.content) {
        displayContent.value = props.messageData.content;
        console.log('🔄 [AgentChatItem] 设置displayContent为完整内容:', props.messageData.content.length, '字符');

        // 强制重新渲染，解决视口外渲染问题
        await nextTick();
        // 触发重排，确保内容正确渲染
        const messageElement = document.querySelector(`[data-message-key="${props.messageData.key}"]`) as HTMLElement;
        if (messageElement) {
          messageElement.style.transform = 'translateZ(0)';
          setTimeout(() => {
            messageElement.style.transform = '';
          }, 0);
        }
      }
    }
  }
);

// 格式化时间显示
const formatTime = (timestamp: string | number) => {
  const date = new Date(Number(timestamp));
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
  const messageDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

  const timeStr = date.toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });

  if (messageDate.getTime() === today.getTime()) {
    return `今天 ${timeStr}`;
  }
  if (messageDate.getTime() === yesterday.getTime()) {
    return `昨天 ${timeStr}`;
  }
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  });
};

// 复制消息内容
const handleCopy = async () => {
  console.log('🔄 [AgentChatItem] 复制按钮被点击');

  try {
    await navigator.clipboard.writeText(props.messageData.content);
    console.log('✅ [AgentChatItem] 复制成功');
    console.log('📋 [AgentChatItem] 复制消息内容:', props.messageData.content);

    // 设置复制状态为true
    isCopied.value = true;

    // 2秒后恢复原始文案
    setTimeout(() => {
      isCopied.value = false;
    }, 2000);
  } catch (error) {
    console.error('复制失败:', error);
    // 降级方案
    const textArea = document.createElement('textarea');
    textArea.value = props.messageData.content;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
    console.log('✅ [AgentChatItem] 降级复制成功');

    // 设置复制状态为true
    isCopied.value = true;

    // 2秒后恢复原始文案
    setTimeout(() => {
      isCopied.value = false;
    }, 2000);
  }
};

// 转发消息
const handleForward = () => {
  console.log('🔄 [AgentChatItem] 转发消息:', props.messageData.content);
  console.log('转发功能开发中...');
};

// 朗读消息
const handleRead = () => {
  console.log('🔄 [AgentChatItem] 朗读消息:', props.messageData.content);

  // 如果正在播放，先停止
  if (isCurrentAudioPlaying(ttsId)) {
    stop();
    isReading.value = false;
  }

  // 构建朗读内容
  const ttsContent = props.messageData.content || '';

  if (ttsContent.trim()) {
    isReading.value = true;
    play({
      id: ttsId,
      text: ttsContent,
      type: 'manualPlay',
    });

    // 监听播放状态变化
    const checkPlayingStatus = () => {
      if (!isCurrentAudioPlaying(ttsId) && audioStatus.value === 'completed') {
        isReading.value = false;
      } else {
        setTimeout(checkPlayingStatus, 100);
      }
    };
    checkPlayingStatus();
  } else {
    console.warn('⚠️ [AgentChatItem] 朗读内容为空');
  }
};

// 复盘消息
const handleReview = () => {
  console.log('🔄 [AgentChatItem] 复盘消息:', props.messageData.content);
  showReviewDialog.value = true;
};

// 处理复盘建议提交
const handleReviewSubmit = (suggestion: string) => {
  reviewSuggestion.value = suggestion;
  showReviewDialog.value = false;
  showReviewConfirmDialog.value = true;
};

// 处理复盘确认
const handleReviewConfirm = () => {
  console.log('🔄 [AgentChatItem] 确认重新生成答案');
  showReviewConfirmDialog.value = false;

  // 这里需要调用重新生成答案的逻辑
  // 将原始问题、回答和复盘建议拼接后发送给后端
  regenerateAnswer();
};

// 重新生成答案
const regenerateAnswer = () => {
  // 获取当前消息的上下文
  const originalQuestion = getOriginalQuestion();
  const originalAnswer = props.messageData.content;
  const suggestion = reviewSuggestion.value;

  // 拼接内容
  const combinedContent = `原始问题：${originalQuestion}\n\n原始回答：${originalAnswer}\n\n复盘建议：${suggestion}\n\n请根据以上信息重新生成一个更好的回答。`;

  console.log('🔄 [AgentChatItem] 重新生成答案内容:', combinedContent);

  // 触发父组件发送新消息
  emit('sendMessage', combinedContent);

  console.log('正在重新生成答案...');
};

// 获取原始问题
const getOriginalQuestion = () => {
  // 如果父组件传入了原始问题，直接使用
  if (props.originalQuestion) {
    return props.originalQuestion;
  }

  // 否则返回占位符，实际使用时父组件应该传入正确的问题
  return '用户的原始问题';
};

// 关闭复盘弹窗
const closeReviewDialogs = () => {
  showReviewDialog.value = false;
  showReviewConfirmDialog.value = false;
  reviewSuggestion.value = '';
};

// 组件挂载时的初始化
onMounted(() => {
  console.log('🔄 [AgentChatItem] 组件挂载完成', {
    role: props.messageData.role,
    isFinish: props.messageData.isFinish,
    hasContent: !!props.messageData.content,
    contentLength: props.messageData.content?.length || 0
  });

  // 如果消息已完成且有内容，直接显示完整内容（历史对话）
  if (props.messageData.isFinish && props.messageData.content && props.messageData.role === 'assistant') {
    displayContent.value = props.messageData.content;
    console.log('🔄 [AgentChatItem] 挂载时设置完整内容（历史对话）:', props.messageData.content.length, '字符');
  } else if (props.messageData.content && props.messageData.role === 'assistant' && !props.messageData.isFinish) {
    // 如果消息未完成但有内容，启动打字机效果
    console.log('🔄 [AgentChatItem] 组件挂载时检测到未完成的消息，启动打字机效果');

    // 设置内容并启动打字机
    typewriter.setContent(props.messageData.content);
    typewriter.start();
  }
});

// 组件卸载时的清理工作
onUnmounted(() => {
  console.log('🔄 [AgentChatItem] 组件卸载，清理打字机和朗读');
  typewriter.destroy();

  // 停止TTS朗读
  if (isReading.value && isCurrentAudioPlaying(ttsId)) {
    stop();
    isReading.value = false;
  }
});
</script>

<style lang="scss" scoped>
.agent-chat-message {
  margin: 24px 0;
  display: flex;
  justify-content: flex-start;
  width: 100%;

  &.is-user {
    justify-content: flex-end;

    .message-container {
      .message-content {
        background: linear-gradient(135deg, #8B7ED8 0%, #B794F6 100%);
        color: white;
        border-radius: 16px 16px 4px 16px;
        border: 2px solid rgba(139, 126, 216, 0.3);

        .user-message-content {
          font-size: 24px;
          font-weight: 500;
          line-height: 1.5;
        }
      }
    }
  }

  .message-container {
    max-width: 85%;
    display: flex;
    flex-direction: column;

    .message-content {
      width: fit-content;
      max-width: 100%;
      padding: 20px;
      background: linear-gradient(180deg, rgb(231, 248, 237) 0%, rgb(212, 250, 226) 75%,rgb(170, 246, 198) 90%, #a7faac 100%);
      border: 2px solid rgba(112, 231, 116, 0.5);
      border-radius: 16px 16px 16px 4px;
      backdrop-filter: blur(20px);

      color: var(--text-primary, #333);
      font-size: 20px;
      font-weight: 400;
      line-height: 1.5;

      // 强制渲染，解决视口外渲染问题
      will-change: contents;
      contain: layout style;

      &.loading-content {
        border-radius: 16px 16px 16px 0px;
      }

      .action-bar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        margin: -12px -12px 16px -12px;
        background: transparent;
        border-bottom: 1px solid var(--border-light, rgba(139, 126, 216, 0.2));
        border-radius: 12px 12px 0 0;

        .timestamp {
          font-size: 18px;
          color: var(--text-secondary, #666);
          font-weight: 400;
        }

        .action-buttons {
          display: flex;
          gap: 16px;

          .action-item {
            font-size: 18px;
            color: var(--text-secondary, #666);
            cursor: pointer;
            user-select: none;
            padding: 4px 8px;
            border-radius: 6px;
          }
        }
      }

      .loading {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 40px;

        .loading-dots {
          display: flex;
          gap: 4px;

          .dot {
            display: inline-block;
            opacity: 0.3;
            animation: dotPulse 0.8s infinite;
            color: var(--primary-color, #8B7ED8);
            font-size: 24px;
            font-weight: bold;
          }

          .dot1 {
            animation-delay: 0s;
          }

          .dot2 {
            animation-delay: 0.25s;
          }

          .dot3 {
            animation-delay: 0.5s;
          }
        }
      }


    }
  }
}

// 动画
@keyframes dotPulse {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 1;
  }
}
</style>
