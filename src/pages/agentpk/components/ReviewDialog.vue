<template>
  <div class="review-dialog-overlay">
    <div class="cyber-enhanced-review-modal" @click="$emit('close')">
      <div class="cyber-enhanced-review-modal__overlay" />

      <div
        class="cyber-enhanced-review-modal__container cyber-enhanced-review-modal__container--review"
        @click.stop
      >
        <button
          class="cyber-enhanced-review-modal__close-btn"
          aria-label="关闭"
          @click="$emit('close')"
        >
          ×
        </button>

        <div class="cyber-enhanced-review-modal__header">
          <h3 class="cyber-enhanced-review-modal__title">
            📝 回答复盘
          </h3>
          <div class="cyber-enhanced-review-modal__divider"></div>
        </div>

        <div class="cyber-enhanced-review-modal__content">
          <!-- 便捷输入部分 -->
          <div class="quick-inputs">
            <p class="quick-inputs-label">便捷输入：</p>
            <div class="quick-inputs-grid">
              <button
                v-for="option in quickOptions"
                :key="option.text"
                class="quick-input-btn"
                @click="handleQuickInput(option.text)"
              >
                <span class="quick-input-text">{{ option.text }}</span>
              </button>
            </div>
          </div>

          <!-- 复盘建议输入框 -->
          <div class="review-input-section">
            <label for="reviewText">复盘建议：</label>
            <textarea
              id="reviewText"
              v-model="reviewSuggestion"
              class="cyber-enhanced-review-modal__textarea"
              placeholder="请输入您的复盘建议..."
              rows="6"
            />
          </div>

          <div class="cyber-enhanced-review-modal__actions">
            <button
              class="review-btn-cancel"
              @click="$emit('close')"
            >
              取消
            </button>
            <button
              class="review-btn-submit"
              :disabled="!reviewSuggestion.trim()"
              @click="handleSubmit"
            >
              提交复盘
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

// Emits定义
const emit = defineEmits<{
  close: [];
  submit: [suggestion: string];
}>();

// 响应式数据
const reviewSuggestion = ref('');

// 便捷输入选项
const quickOptions = [
  { text: '回答更简短' },
  { text: '回答更详细' },
  { text: '搜索更多参考资料' },
  { text: '需要更多数据支撑' },
  { text: '时效性信息需要更新' },
  { text: '需要更多实例说明' },
];

// 处理便捷输入
const handleQuickInput = (text: string) => {
  if (reviewSuggestion.value.trim()) {
    reviewSuggestion.value = `${reviewSuggestion.value}；${text}`;
  } else {
    reviewSuggestion.value = text;
  }
};

// 处理提交
const handleSubmit = () => {
  if (reviewSuggestion.value.trim()) {
    emit('submit', reviewSuggestion.value.trim());
  }
};
</script>

<style lang="scss" scoped>
.review-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease-out;
  pointer-events: auto;
  padding: 40px 20px;
  box-sizing: border-box;
}

.cyber-enhanced-review-modal {
  position: fixed;
  inset: 0;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;

  &__overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.5);
  }

  &__container {
    position: relative;
    max-width: 800px;
    width: 95%;
    padding: 40px;
    border-radius: 24px;
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);

    &--review {
      background: #ffffff; // 白色背景
      border: 1px solid #e5e7eb;
    }
  }

  &__close-btn {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 32px;
    height: 32px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    color: #6b7280;
    font-size: 24px;
    line-height: 1;
    transition: all 0.2s ease;
    border: none;
    background: transparent;

    &:hover {
      color: #374151;
    }
  }

  &__header {
    margin-bottom: 16px;
  }

  &__title {
    margin: 0 0 16px 0;
    font-size: 32px;
    font-weight: 600;
    color: #1f2937;
    text-align: left;
  }

  &__divider {
    width: 100%;
    height: 1px;
    background: #d1d5db;
    margin: 0;
  }

  &__content {
    display: flex;
    flex-direction: column;
    gap: 28px;
  }

  &__textarea {
    width: 100%;
    padding: 20px;
    border: 1px solid #d1d5db;
    border-radius: 12px;
    font-size: 24px;
    line-height: 1.5;
    resize: vertical;
    min-height: 200px;

    &:focus {
      outline: none;
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    &::placeholder {
      color: #9ca3af;
    }
  }

  &__actions {
    display: flex;
    justify-content: flex-end;
    gap: 16px;
  }
}

// 快捷输入样式
.quick-inputs {
  margin-bottom: 24px;

  &-label {
    margin: 0 0 16px 0;
    font-size: 24px;
    font-weight: 500;
    color: #1f2937;
  }

  &-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
}

.quick-input-btn {
  padding: 16px 20px;
  border: 1px solid #d1d5db;
  border-radius: 12px;
  background: #f9fafb;
  color: #1f2937;
  font-size: 22px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 12px;

  &:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
  }

  .quick-input-text {
    flex: 1;
  }
}

// 复盘输入区域
.review-input-section {
  display: flex;
  flex-direction: column;
  gap: 16px;

  label {
    font-size: 24px;
    font-weight: 500;
    color: #1f2937;
  }
}

// 按钮样式
.review-btn-cancel,
.review-btn-submit {
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 24px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;

  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
}

.review-btn-cancel {
  background: #f9fafb;
  border: 1px solid #d1d5db;
  color: #374151;

  &:hover:not(:disabled) {
    background: #f3f4f6;
  }
}

.review-btn-submit {
  background: #3b82f6;
  border: 1px solid #3b82f6;
  color: white;

  &:hover:not(:disabled) {
    background: #2563eb;
  }
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// 响应式设计
@media (max-width: 640px) {
  .cyber-enhanced-review-modal {
    padding: 8px;

    &__container {
      max-width: 100%;
      padding: 16px;
    }

    &__textarea {
      min-height: 100px;
    }

    &__actions {
      flex-direction: column;
      gap: 8px;
    }

    .quick-inputs-grid {
      grid-template-columns: 1fr;
    }
  }
}
</style>
