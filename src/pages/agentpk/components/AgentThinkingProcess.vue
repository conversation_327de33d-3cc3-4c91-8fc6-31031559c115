<template>
  <div class="agent-thinking-process-container">
    <!-- 思考过程和进度条的容器 -->
    <div class="thinking-wrapper">
      <!-- 进度显示组件 -->
      <ThinkingProgressChecklist
        :thinking-data="thinkingData"
        :is-complete="!thinkingData.isLoading && thinkingData.items.length > 0"
      />

      <!-- 思考过程主体 -->
      <div class="agent-thinking-process">
        <!-- 思考过程标题 -->
        <div class="thinking-header">
          <div class="thinking-icon">🤔</div>
          <div class="thinking-title">AI思考过程</div>
        </div>

        <!-- 思考内容列表 -->
        <div class="thinking-content">
        <div
          v-for="(thinkingItem, thinkingIndex) in thinkingData.items"
          :key="thinkingIndex"
          class="thinking-item fade-in"
        >
          <div class="thinking-item-icon">
            <div v-if="thinkingItem.type === 'status'" class="status-dot" :class="thinkingItem.step"></div>
            <div v-else-if="thinkingItem.type === 'question'" class="question-icon">❓</div>
            <div v-else-if="thinkingItem.type === 'log'" class="log-icon">📝</div>
            <div v-else-if="thinkingItem.type === 'search_questions'" class="search-icon">🔍</div>
            <div v-else-if="thinkingItem.type === 'search_result'" class="result-icon">📋</div>
          </div>
          <div class="thinking-item-content">
            <div v-if="thinkingItem.type === 'status'" class="status-message">
              {{ thinkingItem.message }}
            </div>
            <div v-else-if="thinkingItem.type === 'question'" class="question-content">
              <span class="question-label">用户询问：</span>
              <span class="question-text">{{ thinkingItem.question }}</span>
            </div>
            <div v-else-if="thinkingItem.type === 'log'" class="log-content">
              {{ thinkingItem.message }}
            </div>
            <div v-else-if="thinkingItem.type === 'search_questions'" class="search-questions-content">
              <div class="search-questions-label">AI分析如下角度搜索：</div>
              <ul class="questions-list">
                <li v-for="(question, qIndex) in thinkingItem.questions" :key="qIndex" class="question-item">
                  {{ question }}
                </li>
              </ul>
            </div>
            <div v-else-if="thinkingItem.type === 'search_result'" class="search-result-content">
              <div class="search-result-title">搜索结果：</div>
              <div v-for="(result, rIndex) in thinkingItem.results" :key="rIndex" class="result-item">
                <a :href="result.link" target="_blank" class="result-link">
                  {{ result.title }}
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="thinkingData.isLoading" class="loading-indicator">
        <div class="loading-dots">
          <span class="dot dot1">.</span>
          <span class="dot dot2">.</span>
          <span class="dot dot3">.</span>
        </div>
        <span class="loading-text">思考中...</span>
      </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import ThinkingProgressChecklist from './ThinkingProgressChecklist.vue';

interface IThinkingItem {
  type: 'status' | 'question' | 'log' | 'search_questions' | 'search_result';
  message?: string;
  step?: 'start' | 'processing' | 'complete';
  question?: string;
  questions?: string[];
  results?: Array<{ title: string; link: string }>;
}

interface IThinkingData {
  items: IThinkingItem[];
  isLoading: boolean;
}

interface IProps {
  thinkingData: IThinkingData;
}

defineProps<IProps>();
</script>

<style lang="scss" scoped>
.agent-thinking-process-container {
  margin: 16px 0px;
  width: 100%;
  display: flex;
  justify-content: flex-start; // 左对齐
}

.thinking-wrapper {
  display: flex;
  gap: 6px;
  align-items: flex-start;
  width: 100%;
  max-width: 85%; // 限制整体宽度，与聊天气泡保持一致
}

.agent-thinking-process {
  flex: 1; // 占据剩余空间
  background: linear-gradient(135deg, rgba(139, 126, 216, 0.08) 0%, rgba(183, 148, 246, 0.12) 100%);
  border: 2px solid rgba(139, 126, 216, 0.4);
  border-radius: 16px;
  padding: 20px 6px;
  backdrop-filter: blur(20px);

  .thinking-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--border-accent, rgba(139, 126, 216, 0.3));

    .thinking-icon {
      font-size: 18px;
      font-size: 18px;
    }

    .thinking-title {
      font-size: 20px;
      font-weight: 600;
      font-size: 18px;
      font-weight: 500;
      color: var(--text-primary, #333);
    }
  }

  .thinking-content {
    .thinking-item {
      display: flex;
      align-items: flex-start;
      gap: 8px;
      margin-bottom: 6px;
      opacity: 1;
      transform: translateY(0);
      transition: all 0.3s ease;

      &.fade-in {
        opacity: 1;
        transform: translateY(0);
      }

      .thinking-item-icon {
        flex-shrink: 0;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;

        .status-dot {
          width: 12px;
          height: 12px;
          border-radius: 50%;

          &.start {
            background: var(--primary-color, #8B7ED8);
            animation: pulse 1.5s infinite;
          }

          &.processing {
            background: var(--accent-color, #B794F6);
            animation: pulse 1.5s infinite;
          }

          &.complete {
            background: var(--success-color, #10B981);
          }
        }

        .question-icon,
        .log-icon,
        .search-icon,
        .result-icon {
          font-size: 16px;
        }
      }

      .thinking-item-content {
        flex: 1;
        font-size: 20px;
        line-height: 1.5;
        color: var(--text-secondary, #666);

        .status-message {
          color: var(--text-primary, #333);
          font-weight: 500;
        }

        .question-content {
          .question-label {
            color: var(--primary-color, #8B7ED8);
            font-weight: 600;
          }

          .question-text {
            color: var(--text-primary, #333);
          }
        }

        .log-content {
          color: var(--text-secondary, #666);
        }

        .search-questions-content {
          .search-questions-label {
            color: var(--primary-color, #8B7ED8);
            font-weight: 600;
            margin-bottom: 8px;
          }

          .questions-list {
            margin: 0;
            padding-left: 20px;

            .question-item {
              margin-bottom: 4px;
              color: var(--text-secondary, #666);
            }
          }
        }

        .search-result-content {
          .search-result-title {
            color: var(--primary-color, #8B7ED8);
            font-weight: 600;
            margin-bottom: 8px;
          }

          .result-item {
            margin-bottom: 4px;

            .result-link {
              color: var(--text-secondary, #666);
              text-decoration: underline;
              font-style: italic;
              transition: color 0.3s ease;

              &:hover {
                color: var(--primary-color, #8B7ED8);
                text-decoration: underline;
              }
            }
          }
        }
      }
    }
  }

  .loading-indicator {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 0;

    .loading-dots {
      display: flex;
      gap: 4px;

      .dot {
        display: inline-block;
        opacity: 0.3;
        animation: dotPulse 0.8s infinite;
        color: var(--primary-color, #8B7ED8);
        font-size: 16px;
        font-weight: bold;
      }

      .dot1 {
        animation-delay: 0s;
      }

      .dot2 {
        animation-delay: 0.25s;
      }

      .dot3 {
        animation-delay: 0.5s;
      }
    }

    .loading-text {
      color: var(--text-secondary, #666);
      font-size: 14px;
    }
  }
}

// 动画
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes dotPulse {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 1;
  }
}
</style>
